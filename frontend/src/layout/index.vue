<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '240px'" class="sidebar">
      <div class="logo">
        <img src="/logo.svg" alt="Logo" v-if="!isCollapse" />
        <img src="/logo-mini.svg" alt="Logo" v-else />
        <span v-if="!isCollapse" class="logo-text">高考数据平台</span>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <template v-for="route in menuRoutes" :key="route.path">
          <el-menu-item
            v-if="hasPermission(route.meta?.permissions)"
            :index="route.path"
          >
            <el-icon>
              <component :is="getIconComponent(route.meta?.icon)" />
            </el-icon>
            <template #title>{{ route.meta?.title }}</template>
          </el-menu-item>
        </template>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button 
            :icon="isCollapse ? Expand : Fold" 
            @click="toggleCollapse"
            text
          />
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-if="currentRoute.meta?.title">
              {{ currentRoute.meta.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 主题切换 -->
          <el-switch
            v-model="isDark"
            :active-icon="Moon"
            :inactive-icon="Sunny"
            @change="toggleTheme"
          />
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :size="32">
                {{ authStore.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ authStore.user?.username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="main-content">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  Fold,
  Expand,
  Moon,
  Sunny,
  User,
  Setting,
  SwitchButton,
  ArrowDown,
  Dashboard,
  Connection,
  Star,
  School,
  Reading,
  TrendCharts
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const isCollapse = ref(false)
const isDark = ref(false)

// 计算属性
const activeMenu = computed(() => route.path)
const currentRoute = computed(() => route)

// 菜单路由
const menuRoutes = computed(() => {
  return router.getRoutes()
    .filter(route => route.meta?.title && route.path !== '/')
    .sort((a, b) => (a.meta?.order || 0) - (b.meta?.order || 0))
})

// 方法
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

const toggleTheme = () => {
  document.documentElement.classList.toggle('dark', isDark.value)
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

const hasPermission = (permissions?: string[]) => {
  if (!permissions || permissions.length === 0) return true
  return authStore.hasAnyPermission(permissions)
}

const getIconComponent = (iconName?: string) => {
  const iconMap: Record<string, any> = {
    Dashboard,
    Connection,
    Star,
    School,
    Reading,
    TrendCharts,
    User,
    Setting
  }
  return iconMap[iconName || ''] || Dashboard
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      // 打开个人资料对话框
      break
    case 'settings':
      // 打开设置页面
      break
    case 'logout':
      await authStore.logout()
      router.push('/login')
      break
  }
}

// 生命周期
onMounted(() => {
  // 初始化主题
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme === 'dark') {
    isDark.value = true
    document.documentElement.classList.add('dark')
  }
})
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s;
  
  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    border-bottom: 1px solid #e4e7ed;
    
    img {
      height: 32px;
      margin-right: 12px;
    }
    
    .logo-text {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .sidebar-menu {
    border: none;
    height: calc(100vh - 60px);
    
    :deep(.el-menu-item) {
      height: 50px;
      line-height: 50px;
      
      &.is-active {
        background-color: #ecf5ff;
        color: #409eff;
      }
    }
  }
}

.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
      
      .username {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.main-content {
  background: #f5f7fa;
  padding: 20px;
  overflow-y: auto;
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 暗色主题
:global(.dark) {
  .sidebar {
    background: #2b2f3a;
    border-color: #414243;
    
    .logo {
      border-color: #414243;
      
      .logo-text {
        color: #e5eaf3;
      }
    }
    
    .sidebar-menu {
      background: #2b2f3a;
      
      :deep(.el-menu-item) {
        color: #bfcbd9;
        
        &:hover {
          background-color: #363e4f;
        }
        
        &.is-active {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
  
  .header {
    background: #2b2f3a;
    border-color: #414243;
    
    .header-right .user-info {
      &:hover {
        background-color: #363e4f;
      }
      
      .username {
        color: #bfcbd9;
      }
    }
  }
  
  .main-content {
    background: #1d1e1f;
  }
}
</style>
