<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card" v-for="stat in stats" :key="stat.title">
        <div class="stat-content">
          <div class="stat-icon" :style="{ backgroundColor: stat.color }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-title">{{ stat.title }}</div>
          </div>
        </div>
        <div class="stat-trend" v-if="stat.trend">
          <el-icon :class="stat.trend > 0 ? 'trend-up' : 'trend-down'">
            <ArrowUp v-if="stat.trend > 0" />
            <ArrowDown v-else />
          </el-icon>
          <span>{{ Math.abs(stat.trend) }}%</span>
        </div>
      </el-card>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>快捷操作</span>
          </div>
        </template>
        <div class="action-grid">
          <div 
            class="action-item" 
            v-for="action in quickActions" 
            :key="action.title"
            @click="handleQuickAction(action.action)"
          >
            <div class="action-icon" :style="{ backgroundColor: action.color }">
              <el-icon :size="20">
                <component :is="action.icon" />
              </el-icon>
            </div>
            <div class="action-info">
              <div class="action-title">{{ action.title }}</div>
              <div class="action-desc">{{ action.description }}</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>录取趋势分析</span>
                <el-select v-model="trendYear" size="small" style="width: 100px">
                  <el-option label="2024" value="2024" />
                  <el-option label="2023" value="2023" />
                  <el-option label="2022" value="2022" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <!-- <v-chart :option="trendChartOption" style="height: 300px" /> -->
              <div class="chart-placeholder">图表组件加载中...</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>专业热度排行</span>
              </div>
            </template>
            <div class="chart-container">
              <!-- <v-chart :option="popularityChartOption" style="height: 300px" /> -->
              <div class="chart-placeholder">图表组件加载中...</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近活动</span>
            <el-button type="text" @click="viewAllActivities">查看全部</el-button>
          </div>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.time"
            :type="activity.type"
          >
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-desc">{{ activity.description }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  School, 
  Reading, 
  TrendCharts, 
  Connection,
  ArrowUp,
  ArrowDown,
  Star,
  DataAnalysis,
  Download
} from '@element-plus/icons-vue'
// 暂时注释掉ECharts相关导入，避免启动错误
// import VChart from 'vue-echarts'
// import { use } from 'echarts/core'
// import { CanvasRenderer } from 'echarts/renderers'
// import { LineChart, BarChart } from 'echarts/charts'
// import {
//   TitleComponent,
//   TooltipComponent,
//   LegendComponent,
//   GridComponent
// } from 'echarts/components'

// // 注册ECharts组件
// use([
//   CanvasRenderer,
//   LineChart,
//   BarChart,
//   TitleComponent,
//   TooltipComponent,
//   LegendComponent,
//   GridComponent
// ])

const router = useRouter()

// 响应式数据
const trendYear = ref('2024')
const loading = ref(false)

// 统计数据
const stats = ref([
  {
    title: '总院校数',
    value: '2,856',
    icon: School,
    color: '#409eff',
    trend: 5.2
  },
  {
    title: '总专业数',
    value: '12,345',
    icon: Reading,
    color: '#67c23a',
    trend: 8.1
  },
  {
    title: '活跃任务',
    value: '3',
    icon: Connection,
    color: '#e6a23c',
    trend: -2.3
  },
  {
    title: '今日推荐',
    value: '156',
    icon: Star,
    color: '#f56c6c',
    trend: 12.5
  }
])

// 快捷操作
const quickActions = ref([
  {
    title: '开始爬取',
    description: '启动数据收集任务',
    icon: Connection,
    color: '#409eff',
    action: 'startCrawling'
  },
  {
    title: '专业推荐',
    description: '基于位次推荐专业',
    icon: Star,
    color: '#67c23a',
    action: 'recommendation'
  },
  {
    title: '数据分析',
    description: '查看统计分析报告',
    icon: DataAnalysis,
    color: '#e6a23c',
    action: 'analytics'
  },
  {
    title: '数据导出',
    description: '导出Excel报告',
    icon: Download,
    color: '#f56c6c',
    action: 'export'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '数据爬取完成',
    description: '成功爬取2024年重庆市高考录取数据，共计15,234条记录',
    time: '2024-03-20 14:30',
    type: 'success'
  },
  {
    id: 2,
    title: '推荐配置更新',
    description: '用户张三更新了专业推荐配置，调整了权重参数',
    time: '2024-03-20 10:15',
    type: 'info'
  },
  {
    id: 3,
    title: '系统备份',
    description: '系统自动备份完成，备份文件大小：2.3GB',
    time: '2024-03-20 02:00',
    type: 'warning'
  }
])

// 图表配置
const trendChartOption = ref({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['物理类', '历史类']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['2020', '2021', '2022', '2023', '2024']
  },
  yAxis: {
    type: 'value',
    name: '平均分'
  },
  series: [
    {
      name: '物理类',
      type: 'line',
      data: [520, 525, 530, 535, 540],
      smooth: true
    },
    {
      name: '历史类',
      type: 'line',
      data: [510, 515, 520, 525, 530],
      smooth: true
    }
  ]
})

const popularityChartOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: ['计算机科学', '软件工程', '人工智能', '数据科学', '电子信息']
  },
  series: [
    {
      name: '热度分数',
      type: 'bar',
      data: [95, 88, 82, 78, 75],
      itemStyle: {
        color: '#409eff'
      }
    }
  ]
})

// 方法
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'startCrawling':
      router.push('/crawler')
      break
    case 'recommendation':
      router.push('/recommendations')
      break
    case 'analytics':
      router.push('/analytics')
      break
    case 'export':
      ElMessage.info('数据导出功能开发中...')
      break
  }
}

const viewAllActivities = () => {
  router.push('/system')
}

const loadDashboardData = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取实际数据
    // const data = await dashboardApi.getOverview()
    // stats.value = data.stats
    // recentActivities.value = data.activities
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .stat-info {
          flex: 1;
          
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-title {
            font-size: 14px;
            color: #909399;
          }
        }
      }
      
      .stat-trend {
        margin-top: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        
        .trend-up {
          color: #67c23a;
        }
        
        .trend-down {
          color: #f56c6c;
        }
      }
    }
  }
  
  .quick-actions {
    margin-bottom: 20px;
    
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      
      .action-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background-color: #f5f7fa;
          transform: translateY(-2px);
        }
        
        .action-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
        }
        
        .action-info {
          .action-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .action-desc {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 20px;
  }
  
  .recent-activities {
    .activity-content {
      .activity-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 4px;
      }
      
      .activity-desc {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
  }
  
  .chart-container {
    height: 300px;

    .chart-placeholder {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #909399;
      font-size: 14px;
      background: #f5f7fa;
      border-radius: 4px;
    }
  }
}
</style>
