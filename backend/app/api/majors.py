"""
专业管理相关API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel

from ..core.security import get_current_active_user, require_permission
from ..models.user import User, Permission
from ..models.major import Major, MajorCategory

router = APIRouter()


# 响应模型
class MajorResponse(BaseModel):
    id: int
    code: Optional[str]
    name: str
    category: Optional[str]
    degree_type: Optional[str]
    duration: int
    employment_rate: Optional[float]
    average_salary: Optional[int]
    popularity_score: float
    total_enrollments: int
    total_universities: int
    
    class Config:
        from_attributes = True


class MajorDetailResponse(MajorResponse):
    description: Optional[str]
    employment_prospects: Optional[str]
    created_at: str
    updated_at: str


@router.get("/", response_model=List[MajorResponse], summary="获取专业列表")
async def get_majors(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    category: Optional[MajorCategory] = Query(None, description="专业大类筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    order_by: str = Query("name", description="排序字段"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取专业列表"""
    query = Major.all()
    
    # 专业大类筛选
    if category:
        query = query.filter(category=category)
    
    # 搜索筛选
    if search:
        query = query.filter(name__icontains=search)
    
    # 排序
    if order_by == "popularity":
        query = query.order_by("-popularity_score")
    elif order_by == "employment_rate":
        query = query.order_by("-employment_rate")
    elif order_by == "salary":
        query = query.order_by("-average_salary")
    else:
        query = query.order_by("name")
    
    majors = await query.offset(skip).limit(limit).all()
    
    return [MajorResponse.model_validate(major) for major in majors]


@router.get("/{major_id}", response_model=MajorDetailResponse, summary="获取专业详情")
async def get_major(
    major_id: int,
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取指定专业的详情"""
    major = await Major.get_or_none(id=major_id)
    if not major:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="专业不存在"
        )
    
    return MajorDetailResponse.model_validate(major)


@router.get("/categories/list", summary="获取专业大类列表")
async def get_major_categories(
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取所有专业大类列表"""
    return {
        "categories": [
            {"value": category.value, "label": category.value}
            for category in MajorCategory
        ]
    }


@router.get("/stats/overview", summary="获取专业统计概览")
async def get_major_stats(
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取专业统计概览"""
    total_count = await Major.all().count()
    
    # 按专业大类统计
    category_stats = {}
    for category in MajorCategory:
        count = await Major.filter(category=category).count()
        if count > 0:
            category_stats[category.value] = count
    
    # 热门专业（按热度分数排序）
    popular_majors = await Major.all().order_by("-popularity_score").limit(10).values(
        "id", "name", "category", "popularity_score"
    )
    
    # 高就业率专业
    high_employment_majors = await Major.filter(
        employment_rate__gte=0.9
    ).order_by("-employment_rate").limit(10).values(
        "id", "name", "category", "employment_rate"
    )
    
    return {
        "total_count": total_count,
        "category_stats": category_stats,
        "popular_majors": popular_majors,
        "high_employment_majors": high_employment_majors
    }


@router.get("/search/suggestions", summary="获取专业搜索建议")
async def get_major_search_suggestions(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=20, description="返回数量"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取专业搜索建议"""
    majors = await Major.filter(
        name__icontains=q
    ).limit(limit).values("id", "name", "category", "popularity_score")
    
    return {
        "suggestions": [
            {
                "id": major["id"],
                "name": major["name"],
                "category": major["category"],
                "popularity_score": major["popularity_score"]
            }
            for major in majors
        ]
    }


@router.get("/popular/ranking", summary="获取热门专业排行")
async def get_popular_majors_ranking(
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    category: Optional[MajorCategory] = Query(None, description="专业大类筛选"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取热门专业排行榜"""
    query = Major.all()
    
    if category:
        query = query.filter(category=category)
    
    majors = await query.order_by("-popularity_score").limit(limit).all()
    
    ranking = []
    for i, major in enumerate(majors, 1):
        ranking.append({
            "rank": i,
            "id": major.id,
            "name": major.name,
            "category": major.category.value if major.category else None,
            "popularity_score": major.popularity_score,
            "total_enrollments": major.total_enrollments,
            "total_universities": major.total_universities,
            "employment_rate": major.employment_rate,
            "average_salary": major.average_salary
        })
    
    return {"ranking": ranking}
