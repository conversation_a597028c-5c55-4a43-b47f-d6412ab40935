"""
系统管理相关API
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from ..core.security import get_current_active_user, require_permission
from ..models.user import User, Permission
from ..models.system import SystemConfig, BackupRecord

router = APIRouter()


# 请求模型
class SystemConfigCreate(BaseModel):
    key: str
    value: str
    data_type: str = "string"
    category: str
    description: Optional[str] = None
    is_public: bool = False
    is_editable: bool = True


class SystemConfigUpdate(BaseModel):
    value: Optional[str] = None
    description: Optional[str] = None
    is_public: Optional[bool] = None
    is_editable: Optional[bool] = None


# 响应模型
class SystemConfigResponse(BaseModel):
    id: int
    key: str
    value: str
    data_type: str
    category: str
    description: Optional[str]
    is_public: bool
    is_editable: bool
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class BackupRecordResponse(BaseModel):
    id: int
    backup_name: str
    backup_type: str
    file_size: int
    file_size_human: str
    record_count: int
    status: str
    created_at: str
    started_at: str
    completed_at: Optional[str]
    
    class Config:
        from_attributes = True


@router.get("/configs", response_model=List[SystemConfigResponse], summary="获取系统配置")
async def get_system_configs(
    category: Optional[str] = None,
    is_public: Optional[bool] = None,
    current_user: User = Depends(require_permission(Permission.SYSTEM_CONFIG))
):
    """获取系统配置列表"""
    query = SystemConfig.all()
    
    if category:
        query = query.filter(category=category)
    
    if is_public is not None:
        query = query.filter(is_public=is_public)
    
    configs = await query.order_by("category", "key").all()
    
    return [SystemConfigResponse.model_validate(config) for config in configs]


@router.post("/configs", response_model=SystemConfigResponse, summary="创建系统配置")
async def create_system_config(
    config_data: SystemConfigCreate,
    current_user: User = Depends(require_permission(Permission.SYSTEM_CONFIG))
):
    """创建新的系统配置"""
    # 检查配置键是否已存在
    existing_config = await SystemConfig.get_or_none(key=config_data.key)
    if existing_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="配置键已存在"
        )
    
    config = await SystemConfig.create(
        **config_data.dict(),
        created_by=current_user,
        updated_by=current_user
    )
    
    return SystemConfigResponse.model_validate(config)


@router.put("/configs/{config_id}", response_model=SystemConfigResponse, summary="更新系统配置")
async def update_system_config(
    config_id: int,
    config_data: SystemConfigUpdate,
    current_user: User = Depends(require_permission(Permission.SYSTEM_CONFIG))
):
    """更新系统配置"""
    config = await SystemConfig.get_or_none(id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="系统配置不存在"
        )
    
    if not config.is_editable:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该配置不允许编辑"
        )
    
    # 更新配置
    update_data = config_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(config, field, value)
    
    config.updated_by = current_user
    await config.save()
    
    return SystemConfigResponse.model_validate(config)


@router.delete("/configs/{config_id}", summary="删除系统配置")
async def delete_system_config(
    config_id: int,
    current_user: User = Depends(require_permission(Permission.SYSTEM_CONFIG))
):
    """删除系统配置"""
    config = await SystemConfig.get_or_none(id=config_id)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="系统配置不存在"
        )
    
    if not config.is_editable:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该配置不允许删除"
        )
    
    await config.delete()
    
    return {"message": "系统配置删除成功"}


@router.get("/configs/categories", summary="获取配置分类")
async def get_config_categories(
    current_user: User = Depends(require_permission(Permission.SYSTEM_CONFIG))
):
    """获取所有配置分类"""
    categories = await SystemConfig.all().distinct().values_list("category", flat=True)
    
    return {"categories": list(categories)}


@router.get("/configs/public", summary="获取公开配置")
async def get_public_configs(
    current_user: User = Depends(get_current_active_user)
):
    """获取公开的系统配置"""
    configs = await SystemConfig.filter(is_public=True).all()
    
    public_configs = {}
    for config in configs:
        public_configs[config.key] = config.get_typed_value()
    
    return {"configs": public_configs}


@router.get("/backups", response_model=List[BackupRecordResponse], summary="获取备份记录")
async def get_backup_records(
    limit: int = 20,
    offset: int = 0,
    current_user: User = Depends(require_permission(Permission.SYSTEM_BACKUP))
):
    """获取备份记录列表"""
    records = await BackupRecord.all().order_by("-created_at").offset(offset).limit(limit).all()
    
    result = []
    for record in records:
        result.append(BackupRecordResponse(
            id=record.id,
            backup_name=record.backup_name,
            backup_type=record.backup_type,
            file_size=record.file_size,
            file_size_human=record.get_file_size_human(),
            record_count=record.record_count,
            status=record.status,
            created_at=record.created_at.isoformat(),
            started_at=record.started_at.isoformat(),
            completed_at=record.completed_at.isoformat() if record.completed_at else None
        ))
    
    return result


@router.post("/backups/create", summary="创建数据备份")
async def create_backup(
    backup_name: str,
    backup_type: str = "full",
    include_tables: Optional[List[str]] = None,
    current_user: User = Depends(require_permission(Permission.SYSTEM_BACKUP))
):
    """创建数据备份"""
    # 这里应该实现实际的备份逻辑
    # 为了演示，我们创建一个备份记录
    from datetime import datetime
    import os
    
    # 模拟备份文件
    backup_file = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
    file_path = os.path.join("backups", backup_file)
    
    # 创建备份记录
    backup_record = await BackupRecord.create(
        backup_name=backup_name,
        backup_type=backup_type,
        file_path=file_path,
        file_size=0,  # 实际实现时应该是真实文件大小
        included_tables=include_tables or [],
        record_count=0,  # 实际实现时应该是真实记录数
        created_by=current_user,
        started_at=datetime.utcnow(),
        status="pending"
    )
    
    # 这里应该启动异步备份任务
    # 为了演示，我们直接标记为完成
    backup_record.status = "completed"
    backup_record.completed_at = datetime.utcnow()
    await backup_record.save()
    
    return {
        "message": "备份任务创建成功",
        "backup_id": backup_record.id,
        "backup_name": backup_name
    }


@router.get("/status", summary="获取系统状态")
async def get_system_status(
    current_user: User = Depends(get_current_active_user)
):
    """获取系统运行状态"""
    from ..core.database import get_redis
    from tortoise import Tortoise
    import psutil
    import os
    
    # 数据库状态
    db_status = "ok" if Tortoise._connections else "error"
    
    # Redis状态
    redis_client = get_redis()
    redis_status = "ok"
    if redis_client:
        try:
            await redis_client.ping()
        except Exception:
            redis_status = "error"
    else:
        redis_status = "disconnected"
    
    # 系统资源状态
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    return {
        "database": db_status,
        "redis": redis_status,
        "system": {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used": memory.used,
            "memory_total": memory.total,
            "disk_percent": disk.percent,
            "disk_used": disk.used,
            "disk_total": disk.total
        },
        "process": {
            "pid": os.getpid(),
            "uptime": "运行中"  # 实际实现时应该计算真实运行时间
        }
    }


@router.get("/logs/recent", summary="获取最近日志")
async def get_recent_logs(
    limit: int = 100,
    level: Optional[str] = None,
    current_user: User = Depends(require_permission(Permission.SYSTEM_MONITOR))
):
    """获取最近的系统日志"""
    from ..models.system import CrawlLog, LogLevel
    
    query = CrawlLog.all()
    
    if level:
        try:
            log_level = LogLevel(level.upper())
            query = query.filter(level=log_level)
        except ValueError:
            pass
    
    logs = await query.order_by("-created_at").limit(limit).all()
    
    return {
        "logs": [
            {
                "id": log.id,
                "task_id": log.task_id,
                "task_name": log.task_name,
                "level": log.level.value,
                "message": log.message,
                "created_at": log.created_at.isoformat()
            }
            for log in logs
        ]
    }
