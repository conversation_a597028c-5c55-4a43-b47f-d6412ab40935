"""
大学管理相关API
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel

from ..core.security import get_current_active_user, require_permission
from ..models.user import User, Permission
from ..models.university import University, Province

router = APIRouter()


# 响应模型
class UniversityResponse(BaseModel):
    id: int
    code: str
    name: str
    short_name: Optional[str]
    province: str
    city: Optional[str]
    is_985: bool
    is_211: bool
    is_double_first_class: bool
    school_type: Optional[str]
    school_nature: Optional[str]
    website: Optional[str]
    total_majors: int
    ranking_national: Optional[int]
    
    class Config:
        from_attributes = True


class UniversityDetailResponse(UniversityResponse):
    english_name: Optional[str]
    address: Optional[str]
    admission_website: Optional[str]
    phone: Optional[str]
    email: Optional[str]
    total_students: Optional[int]
    faculty_count: Optional[int]
    ranking_provincial: Optional[int]
    created_at: str
    updated_at: str
    last_crawled: Optional[str]


@router.get("/", response_model=List[UniversityResponse], summary="获取大学列表")
async def get_universities(
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    province: Optional[Province] = Query(None, description="省份筛选"),
    is_985: Optional[bool] = Query(None, description="985筛选"),
    is_211: Optional[bool] = Query(None, description="211筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取大学列表"""
    query = University.all()
    
    # 省份筛选
    if province:
        query = query.filter(province=province)
    
    # 985筛选
    if is_985 is not None:
        query = query.filter(is_985=is_985)
    
    # 211筛选
    if is_211 is not None:
        query = query.filter(is_211=is_211)
    
    # 搜索筛选
    if search:
        query = query.filter(name__icontains=search)
    
    universities = await query.offset(skip).limit(limit).order_by("name").all()
    
    return [UniversityResponse.model_validate(uni) for uni in universities]


@router.get("/{university_id}", response_model=UniversityDetailResponse, summary="获取大学详情")
async def get_university(
    university_id: int,
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取指定大学的详情"""
    university = await University.get_or_none(id=university_id)
    if not university:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="大学不存在"
        )
    
    return UniversityDetailResponse.model_validate(university)


@router.get("/provinces/list", summary="获取省份列表")
async def get_provinces(
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取所有省份列表"""
    return {
        "provinces": [
            {"value": province.value, "label": province.value}
            for province in Province
        ]
    }


@router.get("/stats/overview", summary="获取大学统计概览")
async def get_university_stats(
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取大学统计概览"""
    total_count = await University.all().count()
    count_985 = await University.filter(is_985=True).count()
    count_211 = await University.filter(is_211=True).count()
    count_double_first_class = await University.filter(is_double_first_class=True).count()
    
    # 按省份统计
    province_stats = {}
    for province in Province:
        count = await University.filter(province=province).count()
        if count > 0:
            province_stats[province.value] = count
    
    return {
        "total_count": total_count,
        "count_985": count_985,
        "count_211": count_211,
        "count_double_first_class": count_double_first_class,
        "province_stats": province_stats
    }


@router.get("/search/suggestions", summary="获取搜索建议")
async def get_search_suggestions(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=20, description="返回数量"),
    current_user: User = Depends(require_permission(Permission.DATA_VIEW))
):
    """获取大学搜索建议"""
    universities = await University.filter(
        name__icontains=q
    ).limit(limit).values("id", "name", "code", "province")
    
    return {
        "suggestions": [
            {
                "id": uni["id"],
                "name": uni["name"],
                "code": uni["code"],
                "province": uni["province"]
            }
            for uni in universities
        ]
    }
