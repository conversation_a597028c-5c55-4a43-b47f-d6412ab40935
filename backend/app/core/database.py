"""
数据库配置和连接管理
"""
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
from tortoise import Tortoise
from fastapi import FastAPI
import redis.asyncio as redis
from typing import Optional
import logging

from .config import settings

logger = logging.getLogger(__name__)

# Redis连接实例
redis_client: Optional[redis.Redis] = None


async def init_redis():
    """初始化Redis连接"""
    global redis_client
    try:
        redis_client = redis.from_url(
            settings.redis_url,
            encoding="utf-8",
            decode_responses=True,
            socket_connect_timeout=5,
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30,
        )
        # 测试连接
        await redis_client.ping()
        logger.info("Redis连接成功")
    except Exception as e:
        logger.warning(f"Redis连接失败: {e}")
        redis_client = None


async def close_redis():
    """关闭Redis连接"""
    global redis_client
    if redis_client:
        await redis_client.close()
        redis_client = None


def get_redis() -> Optional[redis.Redis]:
    """获取Redis客户端"""
    return redis_client


# Tortoise ORM配置
TORTOISE_ORM = {
    "connections": {"default": settings.database_url},
    "apps": {
        "models": {
            "models": [
                "app.models.user",
                "app.models.university", 
                "app.models.major",
                "app.models.admission",
                "app.models.recommendation",
                "app.models.system",
                "aerich.models"
            ],
            "default_connection": "default",
        },
    },
}


def init_db(app: FastAPI):
    """初始化数据库"""
    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=True,
        add_exception_handlers=True,
    )


async def init_database():
    """手动初始化数据库（用于脚本）"""
    await Tortoise.init(config=TORTOISE_ORM)
    await Tortoise.generate_schemas()


async def close_database():
    """关闭数据库连接"""
    await Tortoise.close_connections()
