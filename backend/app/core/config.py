"""
应用配置管理
统一管理所有配置项，支持环境变量和类型安全验证
"""
from typing import List, Optional
from pydantic import BaseSettings, validator
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "高考数据管理平台"
    app_version: str = "1.0.0"
    debug: bool = False
    api_prefix: str = "/api/v1"
    
    # 数据库配置
    database_url: str = "sqlite://./gaokao.db"
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    
    # JWT认证配置
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    
    # CORS配置
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8080"]
    
    # 安全配置
    rate_limit_per_minute: int = 60
    
    # 爬虫配置
    crawler_api_base_url: str = "https://applet.cqzk.com.cn/prod/history/front/history"
    crawler_api_secret: str = ""
    crawler_max_concurrent: int = 10
    crawler_request_delay: int = 1
    crawler_max_retries: int = 3
    crawler_request_timeout: int = 30
    
    # 文件上传配置
    max_file_size: str = "50MB"
    upload_dir: str = "./uploads"
    
    # 邮件配置
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_user: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_from: Optional[str] = None
    
    # 监控配置
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "./logs/app.log"
    
    @validator("cors_origins", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    @validator("max_file_size")
    def parse_file_size(cls, v):
        """解析文件大小配置"""
        if isinstance(v, str):
            v = v.upper()
            if v.endswith("MB"):
                return int(v[:-2]) * 1024 * 1024
            elif v.endswith("KB"):
                return int(v[:-2]) * 1024
            elif v.endswith("GB"):
                return int(v[:-2]) * 1024 * 1024 * 1024
        return int(v)
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
os.makedirs(settings.upload_dir, exist_ok=True)
