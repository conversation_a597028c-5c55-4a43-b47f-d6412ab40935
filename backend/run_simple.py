"""
简化版后端启动脚本 - 跳过数据库初始化
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 创建简化的FastAPI应用
app = FastAPI(
    title="高考数据管理平台",
    description="智能化高考数据收集、分析和推荐系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "高考数据管理平台API",
        "version": "1.0.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "message": "服务正常运行"}

@app.get("/api/v1/test")
async def test_api():
    """测试API"""
    return {"message": "API测试成功", "data": {"test": True}}

if __name__ == "__main__":
    print("🚀 启动简化版高考数据管理平台...")
    print("📱 前端地址: http://localhost:3000")
    print("🔧 后端地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print()
    
    uvicorn.run(
        "run_simple:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
