# 高考数据管理平台

基于 FastAPI + Vue3 + Tortoise ORM 的现代化高考数据收集、分析和推荐系统。

## 🚀 项目特性

### 核心功能
- **爬虫管理模块**：智能数据收集，支持多年份、多科类数据爬取
- **专业推荐模块**：基于位次的多维度智能推荐算法
- **数据分析模块**：丰富的统计图表和趋势分析
- **用户权限管理**：细粒度的角色权限控制
- **系统管理模块**：配置管理、备份恢复、监控告警

### 技术特色
- **现代化架构**：前后端分离，RESTful API设计
- **高性能**：异步处理，游标分页，Redis缓存
- **可扩展**：策略模式推荐算法，插件化设计
- **用户友好**：响应式设计，暗色主题，实时反馈

## 🏗️ 技术架构

### 后端技术栈
- **框架**：FastAPI 0.104.1
- **数据库**：Tortoise ORM + SQLite/PostgreSQL
- **认证**：JWT + bcrypt
- **任务队列**：Celery + Redis
- **HTTP客户端**：httpx
- **数据处理**：pandas + numpy

### 前端技术栈
- **框架**：Vue 3.4 + Composition API
- **UI库**：Element Plus 2.6
- **状态管理**：Pinia 2.1
- **路由**：Vue Router 4.3
- **图表**：ECharts 5.5 + vue-echarts
- **构建工具**：Vite 5.2 + TypeScript

## 📦 项目结构

```
gaokao/
├── backend/                 # 后端应用
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务服务
│   │   └── main.py         # 应用入口
│   ├── requirements.txt    # Python依赖
│   └── run.py             # 启动脚本
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── api/           # API接口
│   │   ├── components/    # 组件
│   │   ├── layout/        # 布局
│   │   ├── router/        # 路由
│   │   ├── stores/        # 状态管理
│   │   ├── types/         # 类型定义
│   │   ├── views/         # 页面
│   │   └── main.ts        # 应用入口
│   ├── package.json       # 依赖配置
│   └── vite.config.ts     # 构建配置
├── spider/                 # 原有爬虫系统
└── README.md              # 项目文档
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- Redis (可选，用于缓存和任务队列)

### 后端启动

1. **安装依赖**
```bash
cd backend
pip install -r requirements.txt
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他参数
```

3. **初始化数据库**
```bash
# 初始化数据库迁移
aerich init -t app.core.database.TORTOISE_ORM

# 生成迁移文件
aerich init-db

# 应用迁移
aerich upgrade
```

4. **启动服务**
```bash
python run.py
```

后端服务将在 http://localhost:8000 启动

### 前端启动

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

前端应用将在 http://localhost:3000 启动

### 默认账号
- **管理员**：admin / admin123
- **普通用户**：可通过注册页面创建

## 📊 功能模块

### 1. 爬虫管理
- ✅ 可视化任务配置
- ✅ 实时进度监控
- ✅ 日志查看和筛选
- ✅ 任务启停控制
- ✅ 错误处理和重试

### 2. 专业推荐
- ✅ 多维度推荐算法
- ✅ 可配置权重参数
- ✅ 冲稳保比例设置
- ✅ 推荐历史管理
- ✅ 结果导出功能

### 3. 数据分析
- ✅ 录取趋势分析
- ✅ 专业热度排行
- ✅ 分数分布统计
- ✅ 省份竞争分析
- ✅ 交互式图表

### 4. 用户管理
- ✅ 角色权限控制
- ✅ 用户CRUD操作
- ✅ 密码重置
- ✅ 状态管理

### 5. 系统管理
- ✅ 配置参数管理
- ✅ 系统状态监控
- ✅ 备份恢复功能
- ✅ 操作日志记录

## 🔧 配置说明

### 后端配置 (.env)
```env
# 数据库配置
DATABASE_URL=sqlite://./gaokao.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 爬虫配置
CRAWLER_API_SECRET=your-api-secret
CRAWLER_MAX_CONCURRENT=10
CRAWLER_REQUEST_DELAY=1
```

### 前端配置 (vite.config.ts)
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

## 🎯 推荐算法

### 算法原理
推荐系统基于多维度评分机制：

```
总分 = 位次匹配度 × 0.4 + 学校层次 × 0.3 + 专业热度 × 0.2 + 地域偏好 × 0.1
```

### 推荐策略
- **冲刺院校**：位次高于用户1000-3000位
- **稳妥院校**：位次在用户±1000位范围
- **保底院校**：位次低于用户1000-5000位

### 可配置参数
- 各维度权重比例
- 冲稳保推荐比例
- 省份和专业偏好
- 985/211优先级

## 🔒 权限系统

### 角色定义
- **超级管理员**：所有权限
- **数据管理员**：爬虫和数据管理
- **分析师**：数据分析和推荐
- **普通用户**：基础查看和推荐
- **访客**：仅数据查看

### 权限粒度
- 模块级：爬虫、推荐、用户、系统管理
- 操作级：查看、创建、编辑、删除、导出
- 数据级：可访问的数据范围

## 📈 性能优化

### 后端优化
- 异步处理提升并发性能
- 游标分页避免深度分页问题
- Redis缓存热点数据
- 数据库索引优化查询

### 前端优化
- 组件懒加载减少初始包大小
- 虚拟滚动处理大数据量
- 防抖节流优化用户交互
- CDN加速静态资源

## 🚀 部署指南

### Docker部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

### 生产环境
1. 使用PostgreSQL替代SQLite
2. 配置Nginx反向代理
3. 启用HTTPS证书
4. 设置监控告警
5. 定期数据备份

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址：https://github.com/your-username/gaokao
- 问题反馈：https://github.com/your-username/gaokao/issues
- 邮箱：<EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！
